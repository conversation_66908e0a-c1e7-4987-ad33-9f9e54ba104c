import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, PLATFORM_ID, Inject, HostListener, ViewChild, ElementRef } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';

interface Partner {
  name: string;
  logo: string;
  url: string;
  ratio?: number; // Ratio d'échelle pour ajuster la taille du logo
  isHovered?: boolean;
}

@Component({
  selector: 'app-partners',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './partners.component.html',
  styleUrl: './partners.component.css'
})
export class PartnersComponent implements OnInit, OnDestroy {
  @ViewChild('partnersCarousel') partnersCarousel!: ElementRef;

  partners: Partner[] = [
    { name: 'Voyadem', logo: 'assets/images/partners/voyadem-logo.webp', url: 'https://voyadem.ma/', ratio: 1 },
    { name: 'Orastic', logo: 'assets/images/partners/orastic-logo.webp', url: 'https://orastic.ma/', ratio: 0.9 },
    { name: '<PERSON><PERSON><PERSON>' , logo: 'assets/images/partners/tijani-logo.webp', url: 'https://tijani.ma/', ratio: 1 },
    { name: 'Benssassi' , logo: 'assets/images/partners/benssassi-logo.webp', url: 'https://traiteurbenssassi.ma/', ratio: 1 },
    { name: 'Zerouali Tours' , logo: 'assets/images/partners/zeroualitours-logo.webp', url: 'https://zeroualitours.com/', ratio: 1.4 },
    { name: 'Dar Kabbaj', logo: 'assets/images/partners/darkabbaj-logo.webp', url: 'https://darkabbaj.ma/', ratio: 1 },
    { name: 'Monasabat Events', logo: 'assets/images/partners/monasabat-logo.webp', url: 'https://monasabat-events.ma/', ratio: 1 },
  ];

  currentIndex = 0;
  itemsPerView = 5;
  touchedPartners = new Set<string>();
  autoScrollInterval: any = null;
  autoScrollSpeed = 3000; // ms

  // Pour le swipe
  touchStartX = 0;
  touchEndX = 0;
  minSwipeDistance = 50;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.updateItemsPerView();
      this.startAutoScroll();

      // Dupliquer les partenaires pour un défilement infini
      this.partners = [...this.partners, ...this.partners];
    }
  }

  ngOnDestroy(): void {
    this.stopAutoScroll();
  }

  // Méthode pour déterminer si un partenaire est au milieu
  isMiddlePartner(partner: Partner): boolean {
    if (!isPlatformBrowser(this.platformId)) return false;

    // Calculer l'index du milieu en fonction de l'index actuel et du nombre d'éléments visibles
    const middleIndex = Math.floor(this.currentIndex + this.itemsPerView / 2);

    // Trouver l'index du partenaire actuel dans le tableau
    const partnerIndex = this.partners.findIndex(p => p.name === partner.name);

    // Vérifier si le partenaire est au milieu
    // Pour les vues avec un nombre pair d'éléments, considérer deux positions centrales
    if (this.itemsPerView % 2 === 0) {
      // Pour un nombre pair d'éléments visibles, il y a deux positions centrales
      return partnerIndex === middleIndex || partnerIndex === middleIndex - 1;
    } else {
      // Pour un nombre impair d'éléments visibles, il y a une position centrale
      return partnerIndex === middleIndex;
    }
  }

  @HostListener('window:resize')
  updateItemsPerView(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    if (window.innerWidth < 480) {
      this.itemsPerView = 1;
    } else if (window.innerWidth < 640) {
      this.itemsPerView = 2;
    } else if (window.innerWidth < 768) {
      this.itemsPerView = 3;
    } else if (window.innerWidth < 1024) {
      this.itemsPerView = 4;
    } else {
      this.itemsPerView = 5;
    }

    // Ajuster l'index si nécessaire
    const maxIndex = this.partners.length - this.itemsPerView;
    if (this.currentIndex > maxIndex) {
      this.currentIndex = Math.max(0, maxIndex);
    }

    this.updateSliderPosition();
  }

  startAutoScroll(): void {
    this.stopAutoScroll();
    this.autoScrollInterval = setInterval(() => {
      const maxIndex = this.partners.length - this.itemsPerView;
      if (this.currentIndex >= maxIndex) {
        // Revenir au début pour un défilement infini
        this.currentIndex = 0;
      } else {
        this.currentIndex += 1;
      }
      this.updateSliderPosition();
    }, this.autoScrollSpeed);
  }

  stopAutoScroll(): void {
    if (this.autoScrollInterval) {
      clearInterval(this.autoScrollInterval);
      this.autoScrollInterval = null;
    }
  }

  slideNext(): void {
    this.stopAutoScroll(); // Arrêter le défilement auto quand l'utilisateur interagit
    const maxIndex = this.partners.length - this.itemsPerView;
    if (this.currentIndex < maxIndex) {
      // Toujours avancer d'un seul élément à la fois, quelle que soit la taille de l'écran
      this.currentIndex += 1;
      this.updateSliderPosition();
    }
  }

  slidePrev(): void {
    this.stopAutoScroll(); // Arrêter le défilement auto quand l'utilisateur interagit
    if (this.currentIndex > 0) {
      this.currentIndex -= 1;
      this.updateSliderPosition();
    }
  }

  updateSliderPosition(): void {
    if (isPlatformBrowser(this.platformId)) {
      const track = document.querySelector('.partners-track') as HTMLElement;
      if (track) {
        const itemWidth = 100 / this.itemsPerView;
        track.style.transform = `translateX(-${this.currentIndex * itemWidth}%)`;

        // Mettre à jour le partenaire central après chaque déplacement
        setTimeout(() => this.updateCenterPartner(), 50);
      }
    }
  }

  // Ajouter cette méthode pour forcer la mise à jour des classes CSS
  updateCenterPartner(): void {
    if (isPlatformBrowser(this.platformId)) {
      // Réinitialiser tous les logos
      const allLogos = document.querySelectorAll('.partner-logo');
      allLogos.forEach(logo => {
        logo.classList.remove('center-partner');
      });

      // Trouver l'index du milieu
      const middleIndex = Math.floor(this.currentIndex + this.itemsPerView / 2);

      // Appliquer la classe au logo du milieu
      const partnerItems = document.querySelectorAll('.partner-item');
      if (this.itemsPerView % 2 === 0) {
        // Pour un nombre pair d'éléments, appliquer à deux éléments
        if (partnerItems[middleIndex]) {
          const logo = partnerItems[middleIndex].querySelector('.partner-logo');
          if (logo) logo.classList.add('center-partner');
        }
        if (partnerItems[middleIndex - 1]) {
          const logo = partnerItems[middleIndex - 1].querySelector('.partner-logo');
          if (logo) logo.classList.add('center-partner');
        }
      } else {
        // Pour un nombre impair d'éléments, appliquer à un seul élément
        if (partnerItems[middleIndex]) {
          const logo = partnerItems[middleIndex].querySelector('.partner-logo');
          if (logo) logo.classList.add('center-partner');
        }
      }
    }
  }

  // Gestion des événements tactiles pour le swipe
  onTouchStart(event: TouchEvent): void {
    this.stopAutoScroll(); // Arrêter le défilement auto pendant le swipe
    this.touchStartX = event.touches[0].clientX;
  }

  onTouchMove(event: TouchEvent): void {
    this.touchEndX = event.touches[0].clientX;
  }

  onTouchEnd(): void {
    // Calculer la distance du swipe
    const swipeDistance = this.touchEndX - this.touchStartX;

    // Si la distance est suffisante, changer de slide
    if (Math.abs(swipeDistance) > this.minSwipeDistance) {
      if (swipeDistance > 0) {
        this.slidePrev(); // Swipe vers la droite -> slide précédent
      } else {
        this.slideNext(); // Swipe vers la gauche -> slide suivant
      }
    }

    // Réinitialiser les valeurs
    this.touchStartX = 0;
    this.touchEndX = 0;

    // Redémarrer le défilement automatique après un délai
    setTimeout(() => this.startAutoScroll(), 5000);
  }

  handlePartnerClick(event: Event, partnerName: string): boolean {
    if (!isPlatformBrowser(this.platformId)) return true;

    // Sur les appareils tactiles, permettre le clic direct
    // Suppression de la logique de double-tap qui empêchait les clics sur mobile
    return true;
  }

  onPartnerHover(partner: Partner, isHovered: boolean): void {
    partner.isHovered = isHovered;
  }

  getLogoScale(partner: Partner): number {
    const baseRatio = partner.ratio || 1;

    // Si c'est le partenaire du milieu, appliquer un agrandissement de 10%
    if (this.isMiddlePartner(partner)) {
      return baseRatio * 1.1;
    }

    // Si le partenaire est survolé, appliquer un agrandissement de 5%
    if (partner.isHovered) {
      return baseRatio * 1.05;
    }

    // Sinon, retourner le ratio de base
    return baseRatio;
  }
}
