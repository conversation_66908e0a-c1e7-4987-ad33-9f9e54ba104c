import { Component, ElementRef, ViewChild, AfterViewInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Project {
  title: string;
  description: string;
  imageUrl: string;
  url: string;
}

@Component({
  selector: 'app-projects',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './projects.component.html',
  styleUrl: './projects.component.css'
})
export class ProjectsComponent implements AfterViewInit {
  @ViewChild('carouselContainer') carouselContainer!: ElementRef;

  projects: Project[] = [
    {
      title: 'Traiteur Tijani',
      description: 'Site web pour le traiteur Tijani',
      url: 'https://tijani.ma/',
      imageUrl: '/assets/images/projects/tijani.webp'
    },
    {
      title: 'Zerouali Tours',
      description: 'Site web pour la location Zerouali Tours',
      url: 'https://zeroualitours.com/',
      imageUrl: '/assets/images/projects/zeroualitours.webp'
    },
    {
      title: 'Traite<PERSON>',
      description: 'Site web pour le traiteur Benssassi',
      url: 'https://traiteurbenssassi.ma/',
      imageUrl: '/assets/images/projects/benssassi.webp'
    },
    {
      title: 'Monasabat Events',
      description: 'Plateforme pour Monasabat Events Traiteur',
      url: 'https://monasabat-events.ma/',
      imageUrl: '/assets/images/projects/monasabat.webp'
    },
    {
      title: 'Dar Kabbaj',
      description: 'Site web pour le traiteur Dar Kabbaj',
      url: 'https://darkabbaj.ma/',
      imageUrl: '/assets/images/projects/darkabbaj.webp'
    },
    {
      title: 'Orastic',
      description: 'Agence de voyage Orastic',
      url: 'https://orastic.ma/',
      imageUrl: '/assets/images/projects/orastic.webp'
    },
    {
      title: 'Voyadem',
      description: 'Solution SaaS pour agence de voyage',
      url: 'https://voyadem.ma/',
      imageUrl: '/assets/images/projects/voyadem.webp'
    }
  ];

  currentIndex = 0;
  isDragging = false;
  startX = 0;

  ngAfterViewInit() {
    const container = this.carouselContainer.nativeElement;

    container.addEventListener('touchstart', (e: TouchEvent) => this.handleDragStart(e.touches[0].clientX));
    container.addEventListener('touchmove', (e: TouchEvent) => this.handleDragMove(e.touches[0].clientX));
    container.addEventListener('touchend', () => this.handleDragEnd());

    container.addEventListener('mousedown', (e: MouseEvent) => {
      e.preventDefault();
      this.handleDragStart(e.clientX);
    });
    container.addEventListener('mousemove', (e: MouseEvent) => {
      if (this.isDragging) {
        e.preventDefault();
        this.handleDragMove(e.clientX);
      }
    });
    container.addEventListener('mouseup', () => this.handleDragEnd());
    container.addEventListener('mouseleave', () => this.handleDragEnd());
  }

  handleDragStart(clientX: number) {
    this.isDragging = true;
    this.startX = clientX;
  }

  handleDragMove(clientX: number) {
    if (!this.isDragging) return;

    const diffX = clientX - this.startX;
    if (Math.abs(diffX) > 50) {
      if (diffX > 0) {
        this.prevProject();
      } else {
        this.nextProject();
      }
      this.isDragging = false;
    }
  }

  handleDragEnd() {
    this.isDragging = false;
  }

  nextProject() {
    this.currentIndex = (this.currentIndex + 1) % this.projects.length;
  }

  prevProject() {
    this.currentIndex = (this.currentIndex - 1 + this.projects.length) % this.projects.length;
  }

  goToProject(index: number) {
    this.currentIndex = index;
  }

  getPrevIndex(): number {
    return (this.currentIndex - 1 + this.projects.length) % this.projects.length;
  }

  getNextIndex(): number {
    return (this.currentIndex + 1) % this.projects.length;
  }

  visitSite(event: Event, url: string): void {
    // Empêche la propagation pour éviter que le clic sur le bouton
    // ne déclenche également le changement de slide
    event.stopPropagation();

    // Ouvre l'URL dans un nouvel onglet
    window.open(url, '_blank', 'noopener,noreferrer');
  }
}
