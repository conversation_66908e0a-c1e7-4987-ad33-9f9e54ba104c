.carousel-wrapper {
  position: relative;
  padding: 3rem 0;
  overflow: hidden;
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.carousel-container:active {
  cursor: grabbing;
}

.carousel-item {
  position: absolute;
  width: 640px;
  max-width: 90vw; /* Limite la largeur à 90% de la largeur de la fenêtre */
  height: 440px; /* Hauteur fixe pour le ratio */
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 0;
  z-index: 0;
  transform: scale(0.7) translateX(0);
  pointer-events: none;
  user-select: none;
  display: flex;
  flex-direction: column;
  filter: blur(0px);
}

.carousel-item.active {
  opacity: 1;
  transform: scale(1) translateX(0);
  z-index: 20;
  pointer-events: auto;
  filter: blur(0px);
}

.carousel-item.prev {
  opacity: 0.3;
  transform: scale(0.75) translateX(-100%);
  z-index: 10;
  pointer-events: auto;
  filter: blur(1px);
}

.carousel-item.next {
  opacity: 0.3;
  transform: scale(0.75) translateX(100%);
  z-index: 10;
  pointer-events: auto;
  filter: blur(1px);
}

/* Fade-out progressif pour les éléments plus éloignés */
.carousel-item:not(.active):not(.prev):not(.next) {
  opacity: 0.1;
  transform: scale(0.6) translateX(0);
  z-index: 1;
  pointer-events: none;
  filter: blur(2px);
  transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Style de fenêtre Apple pour les projets */
.carousel-card {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  position: relative;
  background-color: white;
  transition: all 0.4s ease;
  display: flex;
  flex-direction: column;
}

.dark .carousel-card {
  background-color: #1e1e1e;
}

/* Barre de titre style Apple */
.apple-titlebar {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background: linear-gradient(to bottom, #f0f0f0, #e6e6e6);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  border-bottom: 1px solid #d1d1d1;
  height: 2.5rem;
  position: relative;
}

.dark .apple-titlebar {
  background: linear-gradient(to bottom, #3a3a3a, #2d2d2d);
  border-bottom: 1px solid #444;
}

/* Boutons de fenêtre Apple */
.window-controls {
  display: flex;
  gap: 0.4rem;
  position: absolute;
  left: 1rem;
}

.window-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.close-button {
  background-color: #ff5f57;
}

.minimize-button {
  background-color: #ffbd2e;
}

.maximize-button {
  background-color: #28c941;
}

/* Titre du projet au centre */
.window-title {
  flex: 1;
  text-align: center;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0 2rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.window-title:hover {
  color: #0090EC;
}

.dark .window-title {
  color: #e0e0e0;
}

.dark .window-title:hover {
  color: #0090EC;
}

/* Contenu de la fenêtre - maintenant c'est juste l'image */
.window-content {
  width: 100%;
  height: 400px; /* Hauteur fixe pour l'image */
  position: relative;
  overflow: hidden;
}

.window-content img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Bouton de visite en haut à droite */
.visit-corner-button {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 20;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
}

.dark .visit-corner-button {
  color: white;
}

.carousel-card:hover .visit-corner-button {
  opacity: 1;
  transform: scale(1);
}

.visit-corner-button:hover {
  background: rgba(0, 144, 236, 0.9);
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 144, 236, 0.3);
  color: white;
}

/* Animation de rotation légère au survol */
.visit-corner-button svg {
  transition: transform 0.3s ease;
}

.visit-corner-button:hover svg {
  transform: rotate(15deg);
}

/* Effet d'élévation sur la carte entière */
.carousel-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.2);
}

/* Adaptation pour les écrans minces */
@media (max-width: 768px) {
  .carousel-container {
    height: 450px;
  }

  .carousel-item {
    height: auto; /* Hauteur auto pour maintenir le ratio */
    aspect-ratio: 16/11; /* Ratio légèrement ajusté pour inclure la barre de titre */
    max-width: 85vw;
  }

  .carousel-item.prev {
    transform: scale(0.85) translateX(-100%);
  }

  .carousel-item.next {
    transform: scale(0.85) translateX(100%);
  }

  .window-content {
    height: 0;
    padding-bottom: 62.5%; /* Ratio 16:10 pour l'image (100% ÷ 16 × 10) */
    position: relative;
  }

  .window-content img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

@media (max-width: 480px) {
  .carousel-container {
    height: 400px;
  }

  .carousel-item {
    max-width: 90vw;
  }

  .carousel-item.prev {
    transform: scale(0.85) translateX(-80%);
  }

  .carousel-item.next {
    transform: scale(0.85) translateX(80%);
  }
}

/* Suppression de l'ancien style de titre en bas */
.project-title {
  display: none;
}

/* Suppression du bouton de visite du site en bas */
.visit-site-button {
  display: none;
}
